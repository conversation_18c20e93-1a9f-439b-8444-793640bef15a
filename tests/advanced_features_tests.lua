--[[
    Advanced Features Tests for Luau Advanced Obfuscator
    
    Comprehensive tests for the new advanced obfuscation features:
    - Encrypted function names with load()
    - Dynamic string generation
    - Multi-stage obfuscation
    - Anti-sandbox detection
    - LuaJIT bytecode embedding
]]

local TestRunner = require(script.Parent.test_runner)
local Obfuscator = require(script.Parent.Parent.src.main)
local DynamicStrings = require(script.Parent.Parent.src.modules.dynamic_strings)
local MultiStage = require(script.Parent.Parent.src.modules.multi_stage)
local AntiSandbox = require(script.Parent.Parent.src.modules.anti_sandbox)
local BytecodeEmbedding = require(script.Parent.Parent.src.modules.bytecode_embedding)

-- Create test runner instance
local runner = TestRunner.new()

-- Test: Dynamic String Generation - Mathematical Construction
runner:addTest("Dynamic String Mathematical Construction", function()
    local dynamicStrings = DynamicStrings.new()
    local testString = "Hello, World!"
    
    local construction = dynamicStrings:generateMathematicalConstruction(testString)
    
    TestRunner.assertType(construction, "table", "Construction should be a table")
    TestRunner.assertType(construction.code, "string", "Construction code should be a string")
    TestRunner.assertType(construction.variable, "string", "Construction variable should be a string")
    TestRunner.assertGreaterThan(#construction.code, 50, "Construction code should be substantial")
    TestRunner.assertContains(construction.code, "string.char", "Should contain string.char calls")
    TestRunner.assertContains(construction.code, construction.variable, "Should reference the result variable")
    
    local stats = dynamicStrings:getStats()
    TestRunner.assertEqual(stats.mathematicalConstructions, 1, "Should have one mathematical construction")
end, "Tests mathematical string construction generation")

-- Test: Dynamic String Generation - Table-Based Construction
runner:addTest("Dynamic String Table-Based Construction", function()
    local dynamicStrings = DynamicStrings.new()
    local testString = "Test123"
    
    local construction = dynamicStrings:generateTableBasedConstruction(testString)
    
    TestRunner.assertType(construction, "table", "Construction should be a table")
    TestRunner.assertType(construction.code, "string", "Construction code should be a string")
    TestRunner.assertContains(construction.code, "local", "Should contain local variable declarations")
    TestRunner.assertContains(construction.code, "string.char", "Should contain string.char calls")
    TestRunner.assertContains(construction.code, "{", "Should contain table definition")
    
    local stats = dynamicStrings:getStats()
    TestRunner.assertGreaterThan(stats.tableBasedConstructions, 0, "Should have table-based constructions")
end, "Tests table-based string construction generation")

-- Test: Dynamic String Generation - Algorithmic Construction
runner:addTest("Dynamic String Algorithmic Construction", function()
    local dynamicStrings = DynamicStrings.new()
    local testString = "Algorithm"
    
    local construction = dynamicStrings:generateAlgorithmicConstruction(testString)
    
    TestRunner.assertType(construction, "table", "Construction should be a table")
    TestRunner.assertContains(construction.code, "function", "Should contain function definition")
    TestRunner.assertContains(construction.code, "for i = 1", "Should contain loop")
    TestRunner.assertContains(construction.code, "string.char", "Should contain string.char calls")
    
    local stats = dynamicStrings:getStats()
    TestRunner.assertGreaterThan(stats.algorithmicGenerations, 0, "Should have algorithmic generations")
end, "Tests algorithmic string construction generation")

-- Test: Multi-Stage Obfuscation - Stage Creation
runner:addTest("Multi-Stage Obfuscation Creation", function()
    local multiStage = MultiStage.new({maxStages = 3})
    local testCode = 'print("Hello from multi-stage!")'
    
    local obfuscatedCode = multiStage:createMultiStageObfuscation(testCode)
    
    TestRunner.assertType(obfuscatedCode, "string", "Obfuscated code should be a string")
    TestRunner.assertGreaterThan(#obfuscatedCode, #testCode * 2, "Should be significantly larger")
    TestRunner.assertContains(obfuscatedCode, "local", "Should contain local variables")
    TestRunner.assertContains(obfuscatedCode, "function", "Should contain functions")
    TestRunner.assertNotContains(obfuscatedCode, "Hello from multi-stage!", "Original string should be encrypted")
    
    local stats = multiStage:getStats()
    TestRunner.assertEqual(stats.stagesCreated, 3, "Should have created 3 stages")
    TestRunner.assertGreaterThan(stats.dynamicLoaders, 0, "Should have dynamic loaders")
end, "Tests multi-stage obfuscation creation")

-- Test: Multi-Stage Obfuscation - Self-Modifying Pattern
runner:addTest("Multi-Stage Self-Modifying Pattern", function()
    local multiStage = MultiStage.new()
    local testCode = 'local x = 42; print(x)'
    
    local pattern = multiStage:createSelfModifyingPattern(testCode)
    
    TestRunner.assertType(pattern, "string", "Pattern should be a string")
    TestRunner.assertContains(pattern, "Self-modifying", "Should contain self-modifying comment")
    TestRunner.assertContains(pattern, "load(", "Should contain load function")
    TestRunner.assertContains(pattern, "gsub", "Should contain string replacement")
end, "Tests self-modifying code pattern generation")

-- Test: Anti-Sandbox Detection - Comprehensive Detection
runner:addTest("Anti-Sandbox Comprehensive Detection", function()
    local antiSandbox = AntiSandbox.new()
    
    local detectionCode = antiSandbox:generateComprehensiveSandboxDetection()
    
    TestRunner.assertType(detectionCode, "string", "Detection code should be a string")
    TestRunner.assertGreaterThan(#detectionCode, 500, "Should be substantial detection code")
    TestRunner.assertContains(detectionCode, "pcall", "Should contain protected calls")
    TestRunner.assertContains(detectionCode, "tick()", "Should contain timing checks")
    TestRunner.assertContains(detectionCode, "collectgarbage", "Should contain memory checks")
    TestRunner.assertContains(detectionCode, "coroutine", "Should contain coroutine checks")
    
    local stats = antiSandbox:getStats()
    TestRunner.assertGreaterThan(stats.sandboxChecks, 0, "Should have sandbox checks")
end, "Tests comprehensive anti-sandbox detection")

-- Test: Anti-Sandbox Detection - Memory Constraint Check
runner:addTest("Anti-Sandbox Memory Constraint Check", function()
    local antiSandbox = AntiSandbox.new()
    local resultVar = "testResult"
    
    local memoryCheck = antiSandbox:generateMemoryConstraintCheck(resultVar)
    
    TestRunner.assertType(memoryCheck, "string", "Memory check should be a string")
    TestRunner.assertContains(memoryCheck, "collectgarbage", "Should contain garbage collection")
    TestRunner.assertContains(memoryCheck, "string.rep", "Should contain memory allocation")
    TestRunner.assertContains(memoryCheck, resultVar, "Should reference result variable")
    
    local stats = antiSandbox:getStats()
    TestRunner.assertGreaterThan(stats.memoryChecks, 0, "Should have memory checks")
end, "Tests memory constraint detection")

-- Test: Anti-Sandbox Detection - VM Detection
runner:addTest("Anti-Sandbox VM Detection", function()
    local antiSandbox = AntiSandbox.new()
    
    local vmDetection = antiSandbox:generateAdvancedVMDetection()
    
    TestRunner.assertType(vmDetection, "string", "VM detection should be a string")
    TestRunner.assertContains(vmDetection, "tick()", "Should contain timing checks")
    TestRunner.assertContains(vmDetection, "math.sqrt", "Should contain computation")
    TestRunner.assertContains(vmDetection, "loadstring", "Should check for function availability")
end, "Tests advanced VM detection")

-- Test: Bytecode Embedding - Basic Embedding
runner:addTest("Bytecode Embedding Basic", function()
    local bytecodeEmbedding = BytecodeEmbedding.new()
    local testCode = 'return "Hello from bytecode!"'
    
    local embeddedCode = bytecodeEmbedding:generateBytecodeEmbedding(testCode)
    
    TestRunner.assertType(embeddedCode, "string", "Embedded code should be a string")
    TestRunner.assertGreaterThan(#embeddedCode, #testCode * 3, "Should be significantly larger")
    TestRunner.assertContains(embeddedCode, "load(", "Should contain load function")
    TestRunner.assertContains(embeddedCode, "bit32.bxor", "Should contain XOR operations")
    TestRunner.assertNotContains(embeddedCode, "Hello from bytecode!", "Original string should be encrypted")
    
    local stats = bytecodeEmbedding:getStats()
    TestRunner.assertEqual(stats.bytecodesEmbedded, 1, "Should have embedded one bytecode")
end, "Tests basic bytecode embedding")

-- Test: Bytecode Embedding - Advanced Protection
runner:addTest("Bytecode Embedding Advanced Protection", function()
    local bytecodeEmbedding = BytecodeEmbedding.new({enableVerification = true})
    local testCode = 'local x = 123; return x * 2'
    
    local protectedCode = bytecodeEmbedding:generateAdvancedBytecodeProtection(testCode)
    
    TestRunner.assertType(protectedCode, "string", "Protected code should be a string")
    TestRunner.assertContains(protectedCode, "verifyAndExecute", "Should contain verification function")
    TestRunner.assertContains(protectedCode, "tick()", "Should contain time checks")
    TestRunner.assertContains(protectedCode, "checksum", "Should contain checksum verification")
    
    local stats = bytecodeEmbedding:getStats()
    TestRunner.assertGreaterThan(stats.verificationChecks, 0, "Should have verification checks")
end, "Tests advanced bytecode protection")

-- Test: Bytecode Embedding - Anti-Debugging
runner:addTest("Bytecode Embedding Anti-Debugging", function()
    local bytecodeEmbedding = BytecodeEmbedding.new()
    local testCode = 'print("Protected code")'
    
    local antiDebugCode = bytecodeEmbedding:generateAntiDebuggingBytecode(testCode)
    
    TestRunner.assertType(antiDebugCode, "string", "Anti-debug code should be a string")
    TestRunner.assertContains(antiDebugCode, "debug.getinfo", "Should contain debug detection")
    TestRunner.assertContains(antiDebugCode, "Debugger detected", "Should contain debugger error")
    TestRunner.assertContains(antiDebugCode, "math.sin", "Should contain timing test")
end, "Tests anti-debugging bytecode embedding")

-- Test: Integration - Full Advanced Obfuscation
runner:addTest("Full Advanced Obfuscation Integration", function()
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = true,
        encryptedFunctionNames = true,
        dynamicStringGeneration = true,
        multiStageObfuscation = true,
        antiSandboxDetection = true,
        bytecodeEmbedding = false, -- Keep disabled for testing
        maxObfuscationStages = 2
    })
    
    local originalCode = [[
local function greet(name)
    local message = "Hello, " .. name .. "!"
    print(message)
    return message
end

local result = greet("Advanced Obfuscation")
print("Result:", result)
]]
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    TestRunner.assertType(obfuscatedCode, "string", "Obfuscated code should be a string")
    TestRunner.assertGreaterThan(#obfuscatedCode, #originalCode * 5, "Should be significantly larger")
    TestRunner.assertNotContains(obfuscatedCode, "Hello,", "Original strings should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "greet", "Function names should be obfuscated")
    TestRunner.assertNotContains(obfuscatedCode, "message", "Variable names should be obfuscated")
    
    -- Check for advanced features
    TestRunner.assertContains(obfuscatedCode, "load(", "Should contain dynamic loading")
    TestRunner.assertContains(obfuscatedCode, "pcall", "Should contain protected calls")
    TestRunner.assertContains(obfuscatedCode, "string.char", "Should contain character construction")
    
    local stats = obfuscator:getStats()
    TestRunner.assertGreaterThan(stats.stringObfuscations.stringsObfuscated, 0, "Should have obfuscated strings")
    TestRunner.assertGreaterThan(stats.structureObfuscations.variablesRenamed, 0, "Should have renamed variables")
    TestRunner.assertGreaterThan(stats.dynamicStrings.dynamicStringsGenerated, 0, "Should have dynamic strings")
    TestRunner.assertGreaterThan(stats.multiStageObfuscation.stagesCreated, 0, "Should have created stages")
end, "Tests full integration of advanced obfuscation features")

-- Run all tests
print("Starting Advanced Features Tests...")
local success = runner:runAll()

if success then
    print("\n🎉 All advanced features tests passed!")
else
    print("\n❌ Some advanced features tests failed.")
end

return success
