--[[
    Execution Tests for Luau Advanced Obfuscator
    
    Tests that verify obfuscated code produces identical results to original code
    and executes correctly in various environments.
]]

local TestRunner = require(script.Parent.test_runner)
local Obfuscator = require(script.Parent.Parent.src.main)

-- Create test runner instance
local runner = TestRunner.new()

-- Helper function to execute code safely and capture results
local function executeCodeSafely(code)
    local results = {}
    local originalPrint = print
    
    -- Capture print output
    print = function(...)
        local args = {...}
        local output = ""
        for i, arg in ipairs(args) do
            if i > 1 then output = output .. "\t" end
            output = output .. tostring(arg)
        end
        table.insert(results, output)
    end
    
    local success, result = pcall(function()
        local chunk = load(code)
        if chunk then
            return chunk()
        else
            error("Failed to load code")
        end
    end)
    
    -- Restore original print
    print = originalPrint
    
    return success, result, results
end

-- Test: Basic Function Execution
runner:addTest("Basic Function Execution", function()
    local originalCode = [[
local function add(a, b)
    return a + b
end

local result = add(5, 3)
print("Result:", result)
return result
]]
    
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = false, -- Disable for execution testing
        multiStageObfuscation = false, -- Disable for simpler testing
        antiSandboxDetection = false -- Disable for testing environment
    })
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Execute original code
    local origSuccess, origResult, origOutput = executeCodeSafely(originalCode)
    TestRunner.assert(origSuccess, "Original code should execute successfully")
    
    -- Execute obfuscated code
    local obfSuccess, obfResult, obfOutput = executeCodeSafely(obfuscatedCode)
    TestRunner.assert(obfSuccess, "Obfuscated code should execute successfully")
    
    -- Compare results
    TestRunner.assertEqual(origResult, obfResult, "Results should be identical")
    TestRunner.assertEqual(#origOutput, #obfOutput, "Output count should be identical")
    
    if #origOutput > 0 and #obfOutput > 0 then
        TestRunner.assertEqual(origOutput[1], obfOutput[1], "Output should be identical")
    end
end, "Tests that obfuscated code produces identical results to original")

-- Test: String Manipulation Execution
runner:addTest("String Manipulation Execution", function()
    local originalCode = [[
local function processString(str)
    local upper = str:upper()
    local lower = str:lower()
    local length = #str
    return upper .. " | " .. lower .. " | " .. length
end

local input = "Hello World"
local result = processString(input)
print(result)
return result
]]
    
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        dynamicStringGeneration = true,
        structureObfuscation = true,
        antiAnalysis = false,
        multiStageObfuscation = false,
        antiSandboxDetection = false
    })
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Execute both versions
    local origSuccess, origResult, origOutput = executeCodeSafely(originalCode)
    local obfSuccess, obfResult, obfOutput = executeCodeSafely(obfuscatedCode)
    
    TestRunner.assert(origSuccess, "Original code should execute successfully")
    TestRunner.assert(obfSuccess, "Obfuscated code should execute successfully")
    TestRunner.assertEqual(origResult, obfResult, "String manipulation results should be identical")
end, "Tests string manipulation in obfuscated code")

-- Test: Loop and Conditional Execution
runner:addTest("Loop and Conditional Execution", function()
    local originalCode = [[
local function fibonacci(n)
    if n <= 1 then
        return n
    end
    return fibonacci(n - 1) + fibonacci(n - 2)
end

local results = {}
for i = 1, 8 do
    results[i] = fibonacci(i)
end

local sum = 0
for _, value in ipairs(results) do
    sum = sum + value
end

print("Sum:", sum)
return sum
]]
    
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = false,
        multiStageObfuscation = false,
        antiSandboxDetection = false
    })
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Execute both versions
    local origSuccess, origResult, origOutput = executeCodeSafely(originalCode)
    local obfSuccess, obfResult, obfOutput = executeCodeSafely(obfuscatedCode)
    
    TestRunner.assert(origSuccess, "Original code should execute successfully")
    TestRunner.assert(obfSuccess, "Obfuscated code should execute successfully")
    TestRunner.assertEqual(origResult, obfResult, "Fibonacci calculation results should be identical")
end, "Tests loops and conditionals in obfuscated code")

-- Test: Table Operations Execution
runner:addTest("Table Operations Execution", function()
    local originalCode = [[
local function processTable(data)
    local result = {}
    
    for key, value in pairs(data) do
        if type(value) == "number" then
            result[key] = value * 2
        elseif type(value) == "string" then
            result[key] = value:upper()
        else
            result[key] = tostring(value)
        end
    end
    
    return result
end

local input = {
    name = "test",
    count = 42,
    active = true,
    score = 3.14
}

local result = processTable(input)
local output = ""
for k, v in pairs(result) do
    output = output .. k .. ":" .. tostring(v) .. " "
end

print(output)
return output
]]
    
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        dynamicStringGeneration = true,
        antiAnalysis = false,
        multiStageObfuscation = false,
        antiSandboxDetection = false
    })
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Execute both versions
    local origSuccess, origResult, origOutput = executeCodeSafely(originalCode)
    local obfSuccess, obfResult, obfOutput = executeCodeSafely(obfuscatedCode)
    
    TestRunner.assert(origSuccess, "Original code should execute successfully")
    TestRunner.assert(obfSuccess, "Obfuscated code should execute successfully")
    
    -- Note: Table iteration order might differ, so we check that both contain the same elements
    TestRunner.assertType(origResult, "string", "Original result should be string")
    TestRunner.assertType(obfResult, "string", "Obfuscated result should be string")
    TestRunner.assertGreaterThan(#origResult, 0, "Original result should not be empty")
    TestRunner.assertGreaterThan(#obfResult, 0, "Obfuscated result should not be empty")
end, "Tests table operations in obfuscated code")

-- Test: Error Handling Execution
runner:addTest("Error Handling Execution", function()
    local originalCode = [[
local function safeOperation(a, b)
    local success, result = pcall(function()
        if b == 0 then
            error("Division by zero")
        end
        return a / b
    end)
    
    if success then
        return result
    else
        return "Error: " .. tostring(result)
    end
end

local result1 = safeOperation(10, 2)
local result2 = safeOperation(10, 0)

print("Result1:", result1)
print("Result2:", result2)

return {result1, result2}
]]
    
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        antiAnalysis = false,
        multiStageObfuscation = false,
        antiSandboxDetection = false
    })
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Execute both versions
    local origSuccess, origResult, origOutput = executeCodeSafely(originalCode)
    local obfSuccess, obfResult, obfOutput = executeCodeSafely(obfuscatedCode)
    
    TestRunner.assert(origSuccess, "Original code should execute successfully")
    TestRunner.assert(obfSuccess, "Obfuscated code should execute successfully")
    
    -- Check that both results are tables with 2 elements
    TestRunner.assertType(origResult, "table", "Original result should be table")
    TestRunner.assertType(obfResult, "table", "Obfuscated result should be table")
    TestRunner.assertEqual(#origResult, #obfResult, "Result arrays should have same length")
    
    -- Check that the successful division result is the same
    TestRunner.assertEqual(origResult[1], obfResult[1], "Successful operation results should be identical")
    
    -- Check that both handle errors (both should contain "Error:")
    TestRunner.assertContains(tostring(origResult[2]), "Error:", "Original should handle error")
    TestRunner.assertContains(tostring(obfResult[2]), "Error:", "Obfuscated should handle error")
end, "Tests error handling in obfuscated code")

-- Test: Complex Nested Functions
runner:addTest("Complex Nested Functions Execution", function()
    local originalCode = [[
local function createCalculator()
    local operations = {
        add = function(a, b) return a + b end,
        subtract = function(a, b) return a - b end,
        multiply = function(a, b) return a * b end,
        divide = function(a, b) 
            if b == 0 then return nil end
            return a / b 
        end
    }
    
    return {
        calculate = function(op, a, b)
            local func = operations[op]
            if func then
                return func(a, b)
            else
                return nil
            end
        end,
        
        getOperations = function()
            local ops = {}
            for name, _ in pairs(operations) do
                table.insert(ops, name)
            end
            return ops
        end
    }
end

local calc = createCalculator()
local result1 = calc.calculate("add", 5, 3)
local result2 = calc.calculate("multiply", 4, 7)
local ops = calc.getOperations()

print("Add result:", result1)
print("Multiply result:", result2)
print("Operations count:", #ops)

return {result1, result2, #ops}
]]
    
    local obfuscator = Obfuscator.new({
        stringObfuscation = true,
        structureObfuscation = true,
        dynamicStringGeneration = true,
        antiAnalysis = false,
        multiStageObfuscation = false,
        antiSandboxDetection = false
    })
    
    local obfuscatedCode = obfuscator:obfuscate(originalCode)
    
    -- Execute both versions
    local origSuccess, origResult, origOutput = executeCodeSafely(originalCode)
    local obfSuccess, obfResult, obfOutput = executeCodeSafely(obfuscatedCode)
    
    TestRunner.assert(origSuccess, "Original code should execute successfully")
    TestRunner.assert(obfSuccess, "Obfuscated code should execute successfully")
    
    TestRunner.assertType(origResult, "table", "Original result should be table")
    TestRunner.assertType(obfResult, "table", "Obfuscated result should be table")
    TestRunner.assertEqual(origResult[1], obfResult[1], "Add results should be identical")
    TestRunner.assertEqual(origResult[2], obfResult[2], "Multiply results should be identical")
    TestRunner.assertEqual(origResult[3], obfResult[3], "Operations count should be identical")
end, "Tests complex nested functions in obfuscated code")

-- Run all tests
print("Starting Execution Tests...")
local success = runner:runAll()

if success then
    print("\n🎉 All execution tests passed!")
    print("✅ Obfuscated code produces identical results to original code")
else
    print("\n❌ Some execution tests failed.")
end

return success
