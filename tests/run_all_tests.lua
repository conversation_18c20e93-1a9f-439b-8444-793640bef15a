--[[
    Comprehensive Test Runner for Luau Advanced Obfuscator
    
    Runs all test suites and provides a comprehensive report of the
    obfuscator's functionality and readiness for deployment.
]]

local TestRunner = require(script.Parent.test_runner)

-- Test suite modules
local IntegrationTests = require(script.Parent.integration_tests)
local AdvancedFeaturesTests = require(script.Parent.advanced_features_tests)
local ExecutionTests = require(script.Parent.execution_tests)
local EdgeCaseTests = require(script.Parent.edge_case_tests)

-- Main test runner
local function runAllTests()
    print("🚀 Starting Comprehensive Luau Advanced Obfuscator Test Suite")
    print("=" * 80)
    print()
    
    local totalTests = 0
    local totalPassed = 0
    local totalFailed = 0
    local testSuites = {}
    
    -- Test suite configurations
    local suites = {
        {
            name = "Integration Tests",
            description = "Core obfuscation functionality and Roblox compatibility",
            module = IntegrationTests,
            critical = true
        },
        {
            name = "Advanced Features Tests", 
            description = "New advanced obfuscation techniques",
            module = AdvancedFeaturesTests,
            critical = true
        },
        {
            name = "Execution Tests",
            description = "Obfuscated code execution and result verification",
            module = ExecutionTests,
            critical = true
        },
        {
            name = "Edge Case Tests",
            description = "Error handling and edge case scenarios",
            module = EdgeCaseTests,
            critical = false
        }
    }
    
    -- Run each test suite
    for i, suite in ipairs(suites) do
        print(string.format("📋 Test Suite %d/%d: %s", i, #suites, suite.name))
        print("Description:", suite.description)
        print("Critical:", suite.critical and "YES" or "NO")
        print("-" * 60)
        
        local startTime = tick()
        local success = false
        
        -- Run the test suite
        if type(suite.module) == "function" then
            success = suite.module()
        else
            print("❌ Error: Test suite module is not a function")
            success = false
        end
        
        local endTime = tick()
        local duration = endTime - startTime
        
        -- Record results
        table.insert(testSuites, {
            name = suite.name,
            success = success,
            duration = duration,
            critical = suite.critical
        })
        
        if success then
            totalPassed = totalPassed + 1
            print(string.format("✅ %s completed successfully (%.2fs)", suite.name, duration))
        else
            totalFailed = totalFailed + 1
            print(string.format("❌ %s failed (%.2fs)", suite.name, duration))
        end
        
        totalTests = totalTests + 1
        print()
    end
    
    -- Generate comprehensive report
    print("=" * 80)
    print("📊 COMPREHENSIVE TEST REPORT")
    print("=" * 80)
    
    -- Summary statistics
    print("📈 Summary Statistics:")
    print(string.format("• Total test suites: %d", totalTests))
    print(string.format("• Passed: %d", totalPassed))
    print(string.format("• Failed: %d", totalFailed))
    print(string.format("• Success rate: %.1f%%", (totalPassed / totalTests) * 100))
    print()
    
    -- Detailed results
    print("📋 Detailed Results:")
    for _, suite in ipairs(testSuites) do
        local status = suite.success and "✅ PASS" or "❌ FAIL"
        local critical = suite.critical and "[CRITICAL]" or "[NON-CRITICAL]"
        print(string.format("• %s %s %s (%.2fs)", status, suite.name, critical, suite.duration))
    end
    print()
    
    -- Critical test analysis
    local criticalPassed = 0
    local criticalTotal = 0
    
    for _, suite in ipairs(testSuites) do
        if suite.critical then
            criticalTotal = criticalTotal + 1
            if suite.success then
                criticalPassed = criticalPassed + 1
            end
        end
    end
    
    print("🔥 Critical Test Analysis:")
    print(string.format("• Critical tests passed: %d/%d", criticalPassed, criticalTotal))
    print(string.format("• Critical success rate: %.1f%%", (criticalPassed / criticalTotal) * 100))
    print()
    
    -- Deployment readiness assessment
    print("🚀 Deployment Readiness Assessment:")
    
    local deploymentReady = true
    local issues = {}
    
    -- Check critical tests
    if criticalPassed < criticalTotal then
        deploymentReady = false
        table.insert(issues, string.format("Critical tests failed: %d/%d", criticalTotal - criticalPassed, criticalTotal))
    end
    
    -- Check overall success rate
    local successRate = (totalPassed / totalTests) * 100
    if successRate < 80 then
        deploymentReady = false
        table.insert(issues, string.format("Overall success rate too low: %.1f%% (minimum 80%%)", successRate))
    end
    
    if deploymentReady then
        print("✅ DEPLOYMENT READY")
        print("🎉 All critical tests passed!")
        print("🛡️ Advanced obfuscation features are working correctly!")
        print("🤖 Discord bot is ready for production deployment!")
        print()
        print("🔧 Features verified:")
        print("• ✅ Encrypted function names with load()")
        print("• ✅ Dynamic string generation")
        print("• ✅ Multi-stage obfuscation")
        print("• ✅ Anti-sandbox detection")
        print("• ✅ LuaJIT bytecode embedding support")
        print("• ✅ Comprehensive error handling")
        print("• ✅ Edge case compatibility")
        print("• ✅ Discord bot integration")
    else
        print("❌ NOT READY FOR DEPLOYMENT")
        print("🚨 Issues found:")
        for _, issue in ipairs(issues) do
            print("• " .. issue)
        end
        print()
        print("🔧 Required actions:")
        print("• Fix failing critical tests")
        print("• Address identified issues")
        print("• Re-run test suite")
        print("• Verify all features work correctly")
    end
    
    print()
    print("=" * 80)
    
    -- Return deployment readiness status
    return deploymentReady, {
        totalTests = totalTests,
        totalPassed = totalPassed,
        totalFailed = totalFailed,
        successRate = successRate,
        criticalPassed = criticalPassed,
        criticalTotal = criticalTotal,
        deploymentReady = deploymentReady,
        issues = issues,
        testSuites = testSuites
    }
end

-- Performance benchmark
local function runPerformanceBenchmark()
    print("⚡ Performance Benchmark")
    print("-" * 40)
    
    local Obfuscator = require(script.Parent.Parent.src.main)
    
    local testCodes = {
        {
            name = "Simple Script",
            code = 'local x = "Hello"; print(x)',
            size = "Small"
        },
        {
            name = "Medium Script", 
            code = string.rep('local var%d = "test%d"; print(var%d); ', 50):format(1,1,1),
            size = "Medium"
        },
        {
            name = "Complex Script",
            code = [[
local function fibonacci(n)
    if n <= 1 then return n end
    return fibonacci(n-1) + fibonacci(n-2)
end

local function processData(data)
    local result = {}
    for i, v in ipairs(data) do
        result[i] = v * 2
    end
    return result
end

for i = 1, 10 do
    print("Fibonacci", i, fibonacci(i))
end
]],
            size = "Large"
        }
    }
    
    local presets = {"basic", "secure", "extreme"}
    
    for _, testCode in ipairs(testCodes) do
        print(string.format("📝 %s (%s):", testCode.name, testCode.size))
        
        for _, preset in ipairs(presets) do
            local config = {
                stringObfuscation = true,
                structureObfuscation = true,
                antiAnalysis = preset ~= "basic",
                encryptedFunctionNames = preset ~= "basic",
                dynamicStringGeneration = preset ~= "basic",
                multiStageObfuscation = preset == "extreme",
                antiSandboxDetection = preset ~= "basic"
            }
            
            local obfuscator = Obfuscator.new(config)
            
            local startTime = tick()
            local obfuscatedCode = obfuscator:obfuscate(testCode.code)
            local endTime = tick()
            
            local processingTime = (endTime - startTime) * 1000 -- Convert to ms
            local sizeIncrease = ((#obfuscatedCode / #testCode.code) - 1) * 100
            
            print(string.format("  • %s: %.2fms, +%.1f%% size", 
                preset:upper(), processingTime, sizeIncrease))
        end
        print()
    end
end

-- Main execution
if script == game:GetService("ReplicatedStorage").tests.run_all_tests then
    -- Run performance benchmark first
    runPerformanceBenchmark()
    
    -- Run comprehensive tests
    local deploymentReady, report = runAllTests()
    
    -- Exit with appropriate code
    if deploymentReady then
        print("🎯 All systems go! Ready for deployment! 🚀")
    else
        print("⚠️ Deployment blocked. Please address issues above.")
    end
    
    return deploymentReady, report
end

return {
    runAllTests = runAllTests,
    runPerformanceBenchmark = runPerformanceBenchmark
}
