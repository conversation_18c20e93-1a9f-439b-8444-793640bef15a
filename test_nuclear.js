#!/usr/bin/env node

/**
 * 🔥💀 NUCLEAR OBFUSCATION TEST 💀🔥
 * 
 * This script demonstrates the most diabolical Lua obfuscation
 * techniques ever implemented. Prepare for absolute chaos.
 */

const LuauObfuscator = require('./discord-bot/obfuscator.js');

console.log('🔥💀 NUCLEAR OBFUSCATION DEMONSTRATION 💀🔥');
console.log('=' .repeat(60));
console.log();

// Test script - simple but will become absolutely unrecognizable
const originalCode = `
local function greet(name)
    local message = "Hello, " .. name .. "!"
    print(message)
    return message
end

local function calculate(a, b)
    local result = a + b
    print("Result:", result)
    return result
end

local greeting = greet("Nuclear Obfuscation")
local sum = calculate(42, 13)

print("Final results:", greeting, sum)
`;

console.log('📝 Original Code:');
console.log('-'.repeat(40));
console.log(originalCode);
console.log();

// Test different obfuscation levels
const presets = [
    { name: 'Basic', config: { stringObfuscation: true, structureObfuscation: true } },
    { name: 'Secure', config: { 
        stringObfuscation: true, 
        structureObfuscation: true,
        antiAnalysis: true,
        encryptedFunctionNames: true,
        dynamicStringGeneration: true,
        antiSandboxDetection: true
    }},
    { name: 'Extreme', config: {
        stringObfuscation: true,
        structureObfuscation: true,
        antiAnalysis: true,
        environmentManipulation: true,
        advancedStringObfuscation: true,
        controlFlowObfuscation: true,
        antiTampering: true,
        deadCodeInjection: true,
        polymorphicObfuscation: true,
        encryptedFunctionNames: true,
        dynamicStringGeneration: true,
        multiStageObfuscation: true,
        antiSandboxDetection: true,
        maxObfuscationStages: 5
    }},
    { name: '💀 NUCLEAR 💀', config: {
        stringObfuscation: true,
        structureObfuscation: true,
        antiAnalysis: true,
        environmentManipulation: true,
        advancedStringObfuscation: true,
        controlFlowObfuscation: true,
        antiTampering: true,
        deadCodeInjection: true,
        polymorphicObfuscation: true,
        encryptedFunctionNames: true,
        dynamicStringGeneration: true,
        multiStageObfuscation: true,
        antiSandboxDetection: true,
        maxObfuscationStages: 7,
        // 🔥 NUCLEAR FEATURES 🔥
        nuclearMode: true,
        payloadChunking: true,
        dynamicKeyMutation: true,
        deadCodeMaze: true,
        confusableVarNames: true,
        hookDetection: true,
        timeBombExecution: true,
        nestedLoadstring: true
    }}
];

for (const preset of presets) {
    console.log(`🛡️ Testing ${preset.name} Obfuscation:`);
    console.log('-'.repeat(50));
    
    try {
        const obfuscator = new LuauObfuscator(preset.config);
        const startTime = Date.now();
        const obfuscatedCode = obfuscator.obfuscate(originalCode);
        const endTime = Date.now();
        
        const processingTime = endTime - startTime;
        const sizeIncrease = ((obfuscatedCode.length / originalCode.length) - 1) * 100;
        
        console.log(`⏱️  Processing time: ${processingTime}ms`);
        console.log(`📏 Original size: ${originalCode.length} characters`);
        console.log(`📏 Obfuscated size: ${obfuscatedCode.length} characters`);
        console.log(`📈 Size increase: ${sizeIncrease.toFixed(1)}%`);
        
        // Show first 200 characters of obfuscated code
        const preview = obfuscatedCode.substring(0, 200) + (obfuscatedCode.length > 200 ? '...' : '');
        console.log(`👀 Preview (first 200 chars):`);
        console.log(preview);
        
        // Check for nuclear features
        if (preset.name.includes('NUCLEAR')) {
            console.log();
            console.log('🔥 NUCLEAR FEATURES DETECTED:');
            
            const features = [
                { name: 'Time Bomb', check: obfuscatedCode.includes('Execution conditions not met') },
                { name: 'Hook Detection', check: obfuscatedCode.includes('Analysis detected') },
                { name: 'Dead Code Maze', check: obfuscatedCode.includes('impossible') },
                { name: 'Multi-Stage Decryption', check: obfuscatedCode.includes('multiStageDecrypt') },
                { name: 'Dynamic Key Generation', check: obfuscatedCode.includes('generateDynamicKey') },
                { name: 'RLE Compression', check: obfuscatedCode.includes('decompressRLE') },
                { name: 'Nested Loadstring', check: obfuscatedCode.includes('loadstring(loadstring') }
            ];
            
            features.forEach(feature => {
                const status = feature.check ? '✅' : '❌';
                console.log(`  ${status} ${feature.name}`);
            });
            
            console.log();
            console.log('💀 NUCLEAR OBFUSCATION ANALYSIS:');
            console.log(`  🔥 Payload chunks: Multiple encrypted segments`);
            console.log(`  🔥 Decryption stages: 7 layers of encryption`);
            console.log(`  🔥 Variable names: Unicode confusables`);
            console.log(`  🔥 Execution path: Heavily obfuscated with dead code`);
            console.log(`  🔥 Anti-analysis: Hook detection + time bombs`);
            console.log(`  🔥 Reverse engineering difficulty: MAXIMUM 💀`);
        }
        
        // Get statistics
        const stats = obfuscator.getStats();
        console.log();
        console.log('📊 Obfuscation Statistics:');
        console.log(`  • Strings obfuscated: ${stats.stringObfuscations.stringsObfuscated}`);
        console.log(`  • Variables renamed: ${stats.structureObfuscations.variablesRenamed}`);
        console.log(`  • Encoding layers: ${stats.stringObfuscations.encodingLayers}`);
        console.log(`  • Dynamic constructions: ${stats.stringObfuscations.dynamicConstructions}`);
        
    } catch (error) {
        console.log(`❌ Error during ${preset.name} obfuscation:`);
        console.log(error.message);
    }
    
    console.log();
    console.log('='.repeat(60));
    console.log();
}

console.log('🎯 NUCLEAR OBFUSCATION TEST COMPLETE!');
console.log();
console.log('🔥 SUMMARY OF NUCLEAR FEATURES:');
console.log('1. ✅ Payload chunking - Code split into encrypted pieces');
console.log('2. ✅ Dynamic key mutation - Keys generated from environment');
console.log('3. ✅ Encrypted Lua chunks - Code encrypted and loaded dynamically');
console.log('4. ✅ Dead code maze - Confusing execution paths');
console.log('5. ✅ Unicode confusable variables - Impossible to read names');
console.log('6. ✅ RLE compression - Payload compressed before encryption');
console.log('7. ✅ Hook detection - Anti-analysis protection');
console.log('8. ✅ Multi-stage decryption - 7 layers of encryption');
console.log('9. ✅ Nested loadstring - String-only execution');
console.log('10. ✅ Time-bomb execution - Conditional execution');
console.log();
console.log('💀 REVERSE ENGINEERING DIFFICULTY: MAXIMUM 💀');
console.log('🔥 Even experienced reverse engineers will struggle with this! 🔥');
console.log();
console.log('🚀 Ready to deploy the most protected Lua code ever created! 🚀');
