--[[
    String Obfuscation Module
    
    Implements multi-layer string encoding with nested character substitution,
    lookup tables, and dynamic string construction using gsub operations.
    
    Based on Project Madara techniques:
    - Multi-layer string encoding
    - Dynamic string construction
    - Nested character substitution with lookup tables
]]

local StringObfuscator = {}
StringObfuscator.__index = StringObfuscator

local Utils = require(script.Parent.utils)

-- Create new string obfuscator instance
function StringObfuscator.new(config)
    local self = setmetatable({}, StringObfuscator)
    
    self.config = config
    self.stats = {
        stringsObfuscated = 0,
        encodingLayers = 0,
        dynamicConstructions = 0
    }
    
    -- Generate lookup table for character substitution
    self.lookupTable = Utils.generateLookupTable(82) -- Project Madara uses 82 chars
    self.substitutionMap = self:generateSubstitutionMap()
    
    return self
end

-- Generate character substitution mapping
function StringObfuscator:generateSubstitutionMap()
    local map = {}
    local reverseMap = {}
    
    -- Create mapping from ASCII to lookup table characters
    local charset = "abcdef<PERSON>ijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?/~`"
    
    for i = 1, math.min(#charset, #self.lookupTable) do
        local original = charset:sub(i, i)
        local substitute = self.lookupTable:sub(i, i)
        map[original] = substitute
        reverseMap[substitute] = original
    end
    
    return {
        encode = map,
        decode = reverseMap
    }
end

-- Apply multi-layer string encoding (Project Madara technique)
function StringObfuscator:encodeString(str)
    if type(str) ~= "string" then
        return str
    end
    
    -- Layer 1: Character substitution
    local layer1 = self:applyCharacterSubstitution(str)
    
    -- Layer 2: Byte manipulation
    local layer2 = self:applyByteManipulation(layer1)
    
    -- Layer 3: Position-dependent encoding
    local layer3 = self:applyPositionDependentEncoding(layer2)
    
    self.stats.encodingLayers = self.stats.encodingLayers + 3
    return layer3
end

-- Apply character substitution using lookup table
function StringObfuscator:applyCharacterSubstitution(str)
    local result = ""
    
    for i = 1, #str do
        local char = str:sub(i, i)
        local substitute = self.substitutionMap.encode[char]
        
        if substitute then
            result = result .. substitute
        else
            -- For characters not in map, use byte value encoding
            local byte = string.byte(char)
            local encoded = self.lookupTable:sub((byte % #self.lookupTable) + 1, (byte % #self.lookupTable) + 1)
            result = result .. encoded
        end
    end
    
    return result
end

-- Apply byte-level manipulation
function StringObfuscator:applyByteManipulation(str)
    local result = {}
    
    for i = 1, #str do
        local byte = string.byte(str, i)
        -- Apply mathematical operations to byte values
        local manipulated = ((byte * 7) + 13) % 256
        table.insert(result, string.char(manipulated))
    end
    
    return table.concat(result)
end

-- Apply position-dependent encoding
function StringObfuscator:applyPositionDependentEncoding(str)
    local result = {}
    
    for i = 1, #str do
        local char = str:sub(i, i)
        local byte = string.byte(char)
        -- Position affects encoding
        local positionKey = (i * 3) % 256
        local encoded = bit32.bxor(byte, positionKey)
        table.insert(result, string.char(encoded))
    end
    
    return table.concat(result)
end

-- Generate dynamic string construction code (gsub technique)
function StringObfuscator:generateDynamicConstruction(originalString)
    local encodedString = self:encodeString(originalString)
    local decoderVar = Utils.generateRandomVariableName()
    local encodedVar = Utils.generateRandomVariableName()
    local resultVar = Utils.generateRandomVariableName()
    
    -- Generate decoder function
    local decoderFunction = self:generateDecoderFunction()
    
    -- Generate construction code
    local constructionCode = string.format([[
local %s = %s
local %s = "%s"
local %s = %s(%s)
]], 
        decoderVar, decoderFunction,
        encodedVar, Utils.escapeString(encodedString),
        resultVar, decoderVar, encodedVar
    )
    
    self.stats.dynamicConstructions = self.stats.dynamicConstructions + 1
    
    return {
        code = constructionCode,
        variable = resultVar
    }
end

-- Generate decoder function for dynamic string construction
function StringObfuscator:generateDecoderFunction()
    local funcName = Utils.generateRandomVariableName()
    local paramName = Utils.generateRandomVariableName()
    local resultName = Utils.generateRandomVariableName()
    local byteName = Utils.generateRandomVariableName()
    local charName = Utils.generateRandomVariableName()
    local indexName = Utils.generateRandomVariableName()
    
    -- Create decoder that reverses the encoding process
    local decoderCode = string.format([[
function(%s)
    local %s = {}
    for %s = 1, #%s do
        local %s = string.byte(%s, %s)
        -- Reverse position-dependent encoding
        local positionKey = (%s * 3) %% 256
        %s = bit32.bxor(%s, positionKey)
        -- Reverse byte manipulation
        %s = ((%s - 13) * 183) %% 256  -- Modular inverse of 7 mod 256 is 183
        table.insert(%s, string.char(%s))
    end
    local decoded = table.concat(%s)
    -- Apply character substitution reversal
    return decoded:gsub(".", function(%s)
        -- Lookup table reversal logic would go here
        return %s
    end)
end]], 
        paramName, resultName, indexName, paramName, byteName, paramName, indexName,
        indexName, byteName, byteName, byteName, byteName, resultName, byteName,
        resultName, charName, charName
    )
    
    return decoderCode
end

-- Obfuscate all strings in AST
function StringObfuscator:obfuscateStrings(ast)
    if not ast or type(ast) ~= "table" then
        return ast
    end
    
    -- Recursively process AST nodes
    for key, value in pairs(ast) do
        if key == "type" and value == "string" then
            -- This is a string literal node
            local stringValue = ast.value
            if stringValue and #stringValue > 0 then
                local construction = self:generateDynamicConstruction(stringValue)
                
                -- Replace string literal with dynamic construction
                ast.type = "dynamic_string"
                ast.construction = construction
                ast.original_value = stringValue
                
                self.stats.stringsObfuscated = self.stats.stringsObfuscated + 1
            end
        elseif type(value) == "table" then
            ast[key] = self:obfuscateStrings(value)
        end
    end
    
    return ast
end

-- Generate gsub-based string manipulation
function StringObfuscator:generateGsubManipulation(str)
    local patterns = {
        {"a", "4"},
        {"e", "3"},
        {"i", "1"},
        {"o", "0"},
        {"s", "5"}
    }
    
    local manipulated = str
    local operations = {}
    
    for _, pattern in ipairs(patterns) do
        if math.random() > 0.5 then -- Randomly apply patterns
            local from, to = pattern[1], pattern[2]
            manipulated = manipulated:gsub(from, to)
            table.insert(operations, {from = to, to = from}) -- Store reverse operation
        end
    end
    
    -- Generate code to reverse the operations
    local reverseCode = string.format('"%s"', Utils.escapeString(manipulated))
    
    for i = #operations, 1, -1 do
        local op = operations[i]
        reverseCode = string.format('%s:gsub("%s", "%s")', 
            reverseCode, Utils.escapeString(op.from), Utils.escapeString(op.to))
    end
    
    return reverseCode
end

-- Create string fragments for complex reconstruction
function StringObfuscator:createStringFragments(str)
    local fragments = {}
    local fragmentSize = math.random(3, 8)
    
    for i = 1, #str, fragmentSize do
        local fragment = str:sub(i, i + fragmentSize - 1)
        if #fragment > 0 then
            table.insert(fragments, self:encodeString(fragment))
        end
    end
    
    return fragments
end

-- Generate encrypted function name with load() execution
function StringObfuscator:generateEncryptedFunctionExecution(functionCode, functionName)
    local encryptedCode = self:encryptCodeChunk(functionCode)
    local decryptorVar = Utils.generateRandomVariableName()
    local encryptedVar = Utils.generateRandomVariableName()
    local keyVar = Utils.generateRandomVariableName()
    local loaderVar = Utils.generateRandomVariableName()
    local executorVar = Utils.generateRandomVariableName()

    -- Generate dynamic key based on function name
    local dynamicKey = self:generateDynamicKeyFromName(functionName)

    local construction = string.format([[
-- Encrypted function execution with load()
local %s = {%s}
local %s = "%s"
local %s = function(data, key)
    local result = ""
    for i = 1, #data do
        local byte = data:byte(i)
        local keyByte = key[(i - 1) %% #key + 1]
        result = result .. string.char(bit32.bxor(byte, keyByte))
    end
    return result
end
local %s = function(code)
    local chunk, err = load(code)
    if chunk then
        return chunk()
    else
        error("Failed to load encrypted function: " .. tostring(err))
    end
end
local %s = %s(%s(%s, %s), _ENV)
]],
        keyVar, table.concat(dynamicKey, ","),
        encryptedVar, Utils.escapeString(encryptedCode),
        decryptorVar,
        loaderVar,
        executorVar, loaderVar, decryptorVar, encryptedVar, keyVar
    )

    self.stats.encryptedFunctions = (self.stats.encryptedFunctions or 0) + 1
    return construction
end

-- Encrypt code chunk using XOR with dynamic key
function StringObfuscator:encryptCodeChunk(code)
    local key = self:generateRandomKey(32)
    local encrypted = ""

    for i = 1, #code do
        local byte = code:byte(i)
        local keyByte = key[(i - 1) % #key + 1]
        encrypted = encrypted .. string.char(bit32.bxor(byte, keyByte))
    end

    return encrypted
end

-- Generate dynamic key from function name
function StringObfuscator:generateDynamicKeyFromName(name)
    local key = {}
    local seed = 0

    -- Create seed from function name
    for i = 1, #name do
        seed = seed + name:byte(i) * i
    end

    -- Generate key using mathematical operations
    for i = 1, 32 do
        local value = ((seed * i * 37) + (i * 13)) % 256
        table.insert(key, value)
    end

    return key
end

-- Generate random encryption key
function StringObfuscator:generateRandomKey(length)
    local key = {}
    for i = 1, length do
        table.insert(key, math.random(1, 255))
    end
    return key
end

-- Generate complex string reconstruction code
function StringObfuscator:generateComplexReconstruction(str)
    local fragments = self:createStringFragments(str)
    local fragmentVars = {}
    local reconstructionCode = ""

    -- Generate variables for each fragment
    for i, fragment in ipairs(fragments) do
        local varName = Utils.generateRandomVariableName()
        fragmentVars[i] = varName
        reconstructionCode = reconstructionCode .. string.format(
            'local %s = "%s"\n', varName, Utils.escapeString(fragment)
        )
    end

    -- Generate concatenation code
    local concatCode = table.concat(fragmentVars, " .. ")
    local resultVar = Utils.generateRandomVariableName()
    reconstructionCode = reconstructionCode .. string.format(
        'local %s = %s\n', resultVar, concatCode
    )
    
    return {
        code = reconstructionCode,
        variable = resultVar
    }
end

-- Get obfuscation statistics
function StringObfuscator:getStats()
    return Utils.deepCopy(self.stats)
end

return StringObfuscator
