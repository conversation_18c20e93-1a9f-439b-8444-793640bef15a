--[[
    Anti-Sandbox Detection Module
    
    Implements sophisticated techniques to detect sandboxed environments,
    analysis tools, and automated deobfuscation systems.
    
    Features:
    - Memory and execution time constraint detection
    - Coroutine timeout detection
    - Invalid system call detection
    - Environment fingerprinting
    - Analysis tool detection
]]

local AntiSandbox = {}
AntiSandbox.__index = AntiSandbox

local Utils = require(script.Parent.utils)

-- Create new anti-sandbox instance
function AntiSandbox.new(config)
    local self = setmetatable({}, AntiSandbox)
    
    self.config = config or {}
    self.stats = {
        sandboxChecks = 0,
        environmentChecks = 0,
        timingChecks = 0,
        memoryChecks = 0,
        coroutineChecks = 0
    }
    
    -- Detection thresholds
    self.thresholds = {
        maxExecutionTime = self.config.maxExecutionTime or 0.1, -- 100ms
        minMemorySize = self.config.minMemorySize or 1024 * 1024, -- 1MB
        maxCoroutineTimeout = self.config.maxCoroutineTimeout or 0.05 -- 50ms
    }
    
    return self
end

-- Generate comprehensive anti-sandbox checks
function AntiSandbox:generateAntiSandboxCode()
    local checkVar = Utils.generateRandomVariableName()
    local resultVar = Utils.generateRandomVariableName()
    local timeVar = Utils.generateRandomVariableName()
    
    local code = string.format([[
-- Anti-sandbox detection system
local %s = function()
    local %s = {}
    local %s = tick()
    
    %s
    %s
    %s
    %s
    %s
    
    -- Evaluate all checks
    for _, check in ipairs(%s) do
        if not check then
            return false
        end
    end
    
    return true
end

-- Execute sandbox check
if not %s() then
    error("Execution environment not supported")
end
]], 
        checkVar, resultVar, timeVar,
        self:generateMemoryConstraintCheck(resultVar),
        self:generateExecutionTimeCheck(resultVar, timeVar),
        self:generateCoroutineTimeoutCheck(resultVar),
        self:generateSystemCallCheck(resultVar),
        self:generateEnvironmentFingerprintCheck(resultVar),
        resultVar,
        checkVar
    )
    
    self.stats.sandboxChecks = self.stats.sandboxChecks + 1
    return code
end

-- Generate memory constraint detection
function AntiSandbox:generateMemoryConstraintCheck(resultVar)
    local memVar = Utils.generateRandomVariableName()
    local testVar = Utils.generateRandomVariableName()
    
    local check = string.format([[
    -- Memory constraint check
    local %s = 0
    local %s = {}
    
    -- Try to allocate memory and measure
    local success = pcall(function()
        for i = 1, 1000 do
            %s[i] = string.rep("x", 1024) -- 1KB per iteration
        end
        %s = collectgarbage("count") * 1024
    end)
    
    table.insert(%s, success and %s > %d)
]], 
        memVar, testVar,
        testVar,
        memVar,
        resultVar, memVar, self.thresholds.minMemorySize
    )
    
    self.stats.memoryChecks = self.stats.memoryChecks + 1
    return check
end

-- Generate execution time constraint detection
function AntiSandbox:generateExecutionTimeCheck(resultVar, timeVar)
    local loopVar = Utils.generateRandomVariableName()
    local endTimeVar = Utils.generateRandomVariableName()
    
    local check = string.format([[
    -- Execution time check
    local %s = 0
    for i = 1, 10000 do
        %s = %s + math.sin(i) * math.cos(i)
    end
    local %s = tick()
    
    -- Check if execution was artificially slowed
    local executionTime = %s - %s
    table.insert(%s, executionTime < %f)
]], 
        loopVar,
        loopVar, loopVar,
        endTimeVar,
        endTimeVar, timeVar,
        resultVar, self.thresholds.maxExecutionTime
    )
    
    self.stats.timingChecks = self.stats.timingChecks + 1
    return check
end

-- Generate coroutine timeout detection
function AntiSandbox:generateCoroutineTimeoutCheck(resultVar)
    local coVar = Utils.generateRandomVariableName()
    local startVar = Utils.generateRandomVariableName()
    local endVar = Utils.generateRandomVariableName()
    
    local check = string.format([[
    -- Coroutine timeout check
    local %s = coroutine.create(function()
        local %s = tick()
        -- Simulate work
        for i = 1, 1000 do
            coroutine.yield()
        end
        local %s = tick()
        return %s - %s
    end)
    
    local success, duration = pcall(function()
        local startTime = tick()
        while coroutine.status(%s) ~= "dead" do
            coroutine.resume(%s)
            if tick() - startTime > %f then
                return false -- Timeout detected
            end
        end
        return true
    end)
    
    table.insert(%s, success)
]], 
        coVar, startVar,
        endVar,
        endVar, startVar,
        coVar,
        coVar, self.thresholds.maxCoroutineTimeout,
        resultVar
    )
    
    self.stats.coroutineChecks = self.stats.coroutineChecks + 1
    return check
end

-- Generate invalid system call detection
function AntiSandbox:generateSystemCallCheck(resultVar)
    local testVar = Utils.generateRandomVariableName()
    
    local check = string.format([[
    -- System call availability check
    local %s = true
    
    -- Test for restricted functions
    local restrictedFunctions = {
        "io", "os", "debug", "package", "require"
    }
    
    for _, funcName in ipairs(restrictedFunctions) do
        if _G[funcName] == nil then
            %s = false
            break
        end
    end
    
    -- Test debug.getinfo availability
    local debugSuccess = pcall(function()
        return debug.getinfo(1)
    end)
    
    %s = %s and debugSuccess
    table.insert(%s, %s)
]], 
        testVar,
        testVar,
        testVar, testVar,
        resultVar, testVar
    )
    
    return check
end

-- Generate environment fingerprinting
function AntiSandbox:generateEnvironmentFingerprintCheck(resultVar)
    local fingerprintVar = Utils.generateRandomVariableName()
    local expectedVar = Utils.generateRandomVariableName()
    
    local check = string.format([[
    -- Environment fingerprinting
    local %s = 0
    local %s = 0
    
    -- Calculate environment fingerprint
    for k, v in pairs(_G) do
        %s = %s + #tostring(k) + #tostring(type(v))
    end
    
    -- Expected fingerprint for normal Lua environment
    %s = 2847 -- Approximate expected value
    
    -- Check if fingerprint is within reasonable range
    local fingerprintDiff = math.abs(%s - %s)
    table.insert(%s, fingerprintDiff < 500)
]], 
        fingerprintVar, expectedVar,
        fingerprintVar, fingerprintVar,
        expectedVar,
        fingerprintVar, expectedVar,
        resultVar
    )
    
    self.stats.environmentChecks = self.stats.environmentChecks + 1
    return check
end

-- Generate analysis tool detection
function AntiSandbox:generateAnalysisToolDetection()
    local detectorVar = Utils.generateRandomVariableName()
    local toolsVar = Utils.generateRandomVariableName()
    
    local detection = string.format([[
-- Analysis tool detection
local %s = function()
    local %s = {
        -- Common analysis tool signatures
        "luac", "luadec", "unluac", "luajit",
        "ida", "ghidra", "x64dbg", "ollydbg",
        "wireshark", "fiddler", "burp"
    }
    
    -- Check for tool-specific environment variables
    for _, tool in ipairs(%s) do
        if _G[tool] or _G[tool:upper()] then
            return true -- Tool detected
        end
    end
    
    -- Check for debugging indicators
    local debugIndicators = {
        function() return debug.getinfo(3) ~= nil end,
        function() return debug.getlocal(2, 1) ~= nil end,
        function() return debug.getupvalue(function() end, 1) ~= nil end
    }
    
    for _, indicator in ipairs(debugIndicators) do
        local success, result = pcall(indicator)
        if success and result then
            return true -- Debugging detected
        end
    end
    
    return false
end

if %s() then
    error("Analysis tool detected")
end
]], 
        detectorVar, toolsVar,
        toolsVar,
        detectorVar
    )
    
    return detection
end

-- Generate VM detection with advanced heuristics
function AntiSandbox:generateAdvancedVMDetection()
    local vmDetectorVar = Utils.generateRandomVariableName()
    local heuristicsVar = Utils.generateRandomVariableName()
    
    local detection = string.format([[
-- Advanced VM detection
local %s = function()
    local %s = {}
    
    -- Timing-based detection
    local startTime = tick()
    for i = 1, 10000 do
        math.sqrt(i)
    end
    local endTime = tick()
    table.insert(%s, (endTime - startTime) < 0.1)
    
    -- Memory allocation pattern detection
    local memBefore = collectgarbage("count")
    local testTable = {}
    for i = 1, 1000 do
        testTable[i] = string.rep("test", 100)
    end
    local memAfter = collectgarbage("count")
    table.insert(%s, (memAfter - memBefore) > 100)
    
    -- Function availability heuristics
    local functions = {
        "loadstring", "load", "pcall", "xpcall",
        "getfenv", "setfenv", "rawget", "rawset"
    }
    
    local availableCount = 0
    for _, func in ipairs(functions) do
        if _G[func] then
            availableCount = availableCount + 1
        end
    end
    table.insert(%s, availableCount >= #functions * 0.8)
    
    -- Check all heuristics
    local passedChecks = 0
    for _, check in ipairs(%s) do
        if check then
            passedChecks = passedChecks + 1
        end
    end
    
    return passedChecks >= #%s * 0.7
end

if not %s() then
    error("Virtual machine environment detected")
end
]], 
        vmDetectorVar, heuristicsVar,
        heuristicsVar,
        heuristicsVar,
        heuristicsVar,
        heuristicsVar,
        heuristicsVar,
        vmDetectorVar
    )
    
    return detection
end

-- Generate comprehensive sandbox detection
function AntiSandbox:generateComprehensiveSandboxDetection()
    local mainCode = self:generateAntiSandboxCode()
    local toolDetection = self:generateAnalysisToolDetection()
    local vmDetection = self:generateAdvancedVMDetection()
    
    return mainCode .. "\n" .. toolDetection .. "\n" .. vmDetection
end

-- Get statistics
function AntiSandbox:getStats()
    return {
        sandboxChecks = self.stats.sandboxChecks,
        environmentChecks = self.stats.environmentChecks,
        timingChecks = self.stats.timingChecks,
        memoryChecks = self.stats.memoryChecks,
        coroutineChecks = self.stats.coroutineChecks
    }
end

return AntiSandbox
