--[[
    Dynamic String Generation Module
    
    Creates mechanisms to build key strings using mathematical operations
    or table manipulation instead of string literals. This prevents static
    string analysis and makes reverse engineering extremely difficult.
    
    Advanced Features:
    - Mathematical string construction
    - Table-based string building
    - Algorithmic character generation
    - Runtime string assembly
]]

local DynamicStrings = {}
DynamicStrings.__index = DynamicStrings

local Utils = require(script.Parent.utils)

-- Create new dynamic strings instance
function DynamicStrings.new(config)
    local self = setmetatable({}, DynamicStrings)
    
    self.config = config or {}
    self.stats = {
        dynamicStringsGenerated = 0,
        mathematicalConstructions = 0,
        tableBasedConstructions = 0,
        algorithmicGenerations = 0
    }
    
    -- Character sets for different construction methods
    self.charSets = {
        ascii = {},
        printable = {},
        alphanumeric = {}
    }
    
    -- Initialize character sets
    self:initializeCharSets()
    
    return self
end

-- Initialize character sets for dynamic generation
function DynamicStrings:initializeCharSets()
    -- ASCII character set (32-126)
    for i = 32, 126 do
        table.insert(self.charSets.ascii, i)
    end
    
    -- Printable characters
    local printableChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?"
    for i = 1, #printableChars do
        table.insert(self.charSets.printable, printableChars:byte(i))
    end
    
    -- Alphanumeric characters
    local alphanumericChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    for i = 1, #alphanumericChars do
        table.insert(self.charSets.alphanumeric, alphanumericChars:byte(i))
    end
end

-- Generate dynamic string construction using mathematical operations
function DynamicStrings:generateMathematicalConstruction(targetString)
    local constructionVar = Utils.generateRandomVariableName()
    local resultVar = Utils.generateRandomVariableName()
    local tempVar = Utils.generateRandomVariableName()
    
    local construction = string.format([[
-- Mathematical string construction
local %s = {}
local %s = ""
]], constructionVar, resultVar)
    
    -- Generate mathematical operations for each character
    for i = 1, #targetString do
        local char = targetString:sub(i, i)
        local charCode = char:byte()
        local mathOp = self:generateMathematicalOperation(charCode)
        
        construction = construction .. string.format([[
local %s = %s
%s = %s .. string.char(%s)
]], tempVar .. i, mathOp, resultVar, resultVar, tempVar .. i)
    end
    
    self.stats.mathematicalConstructions = self.stats.mathematicalConstructions + 1
    self.stats.dynamicStringsGenerated = self.stats.dynamicStringsGenerated + 1
    
    return {
        code = construction,
        variable = resultVar
    }
end

-- Generate mathematical operation that results in target value
function DynamicStrings:generateMathematicalOperation(targetValue)
    local operations = {
        -- Simple arithmetic
        function() 
            local a = math.random(1, 100)
            local b = targetValue - a
            return string.format("(%d + %d)", a, b)
        end,
        
        -- Multiplication and division
        function()
            local divisor = math.random(2, 10)
            local multiplier = targetValue * divisor
            return string.format("(%d / %d)", multiplier, divisor)
        end,
        
        -- Modular arithmetic
        function()
            local mod = math.random(256, 512)
            local base = targetValue + mod
            return string.format("(%d %% %d)", base, mod)
        end,
        
        -- Bitwise operations
        function()
            local xorKey = math.random(1, 255)
            local xorValue = bit32.bxor(targetValue, xorKey)
            return string.format("bit32.bxor(%d, %d)", xorValue, xorKey)
        end,
        
        -- Complex expression
        function()
            local a = math.random(1, 50)
            local b = math.random(1, 50)
            local c = targetValue - (a * b)
            return string.format("((%d * %d) + %d)", a, b, c)
        end
    }
    
    local operation = operations[math.random(1, #operations)]
    return operation()
end

-- Generate table-based string construction
function DynamicStrings:generateTableBasedConstruction(targetString)
    local tableVar = Utils.generateRandomVariableName()
    local resultVar = Utils.generateRandomVariableName()
    local indexVar = Utils.generateRandomVariableName()
    
    -- Create character lookup table with obfuscated indices
    local charTable = {}
    local indexMap = {}
    
    for i = 1, #targetString do
        local char = targetString:sub(i, i)
        local obfuscatedIndex = self:generateObfuscatedIndex(i)
        charTable[obfuscatedIndex] = char:byte()
        table.insert(indexMap, obfuscatedIndex)
    end
    
    local construction = string.format([[
-- Table-based string construction
local %s = {]], tableVar)
    
    -- Add table entries with mathematical indices
    for index, charCode in pairs(charTable) do
        construction = construction .. string.format("[%s] = %d, ", 
            self:generateMathematicalOperation(index), charCode)
    end
    
    construction = construction .. string.format([[}
local %s = ""
]], resultVar)
    
    -- Generate table access code
    for i, index in ipairs(indexMap) do
        construction = construction .. string.format([[
%s = %s .. string.char(%s[%s])
]], resultVar, resultVar, tableVar, self:generateMathematicalOperation(index))
    end
    
    self.stats.tableBasedConstructions = self.stats.tableBasedConstructions + 1
    self.stats.dynamicStringsGenerated = self.stats.dynamicStringsGenerated + 1
    
    return {
        code = construction,
        variable = resultVar
    }
end

-- Generate obfuscated index for table access
function DynamicStrings:generateObfuscatedIndex(originalIndex)
    -- Use mathematical transformation to obfuscate index
    return (originalIndex * 37 + 13) % 1000 + 1000
end

-- Generate algorithmic string construction
function DynamicStrings:generateAlgorithmicConstruction(targetString)
    local algorithmVar = Utils.generateRandomVariableName()
    local resultVar = Utils.generateRandomVariableName()
    local seedVar = Utils.generateRandomVariableName()
    
    -- Create seed based on string properties
    local seed = 0
    for i = 1, #targetString do
        seed = seed + targetString:byte(i) * i
    end
    
    local construction = string.format([[
-- Algorithmic string construction
local %s = %d
local %s = ""
local %s = function(s, len)
    local result = ""
    for i = 1, len do
        local charCode = ((s * i * 31) + (i * 17)) %% 95 + 32
        result = result .. string.char(charCode)
    end
    return result
end
]], seedVar, seed, resultVar, algorithmVar)
    
    -- Generate algorithm that produces target string
    construction = construction .. string.format([[
-- Generate base string and transform
local baseString = %s(%s, %d)
]], algorithmVar, seedVar, #targetString)
    
    -- Add transformation logic to get exact target
    construction = construction .. self:generateStringTransformation(targetString)
    
    self.stats.algorithmicGenerations = self.stats.algorithmicGenerations + 1
    self.stats.dynamicStringsGenerated = self.stats.dynamicStringsGenerated + 1
    
    return {
        code = construction,
        variable = resultVar
    }
end

-- Generate string transformation to match target
function DynamicStrings:generateStringTransformation(targetString)
    local transformVar = Utils.generateRandomVariableName()
    local transformation = string.format([[
local %s = {}
]], transformVar)
    
    for i = 1, #targetString do
        local targetChar = targetString:byte(i)
        transformation = transformation .. string.format([[
%s[%d] = %d
]], transformVar, i, targetChar)
    end
    
    transformation = transformation .. string.format([[
for i = 1, #%s do
    %s = %s .. string.char(%s[i])
end
]], transformVar, "result", "result", transformVar)
    
    return transformation
end

-- Get statistics
function DynamicStrings:getStats()
    return {
        dynamicStringsGenerated = self.stats.dynamicStringsGenerated,
        mathematicalConstructions = self.stats.mathematicalConstructions,
        tableBasedConstructions = self.stats.tableBasedConstructions,
        algorithmicGenerations = self.stats.algorithmicGenerations
    }
end

return DynamicStrings
