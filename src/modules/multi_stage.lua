--[[
    Multi-Stage Obfuscation Module
    
    Implements layered obfuscation where one layer decrypts another script blob,
    which in turn fetches or executes additional obfuscated code. This creates
    multiple layers of protection that must be peeled away sequentially.
    
    Features:
    - Multi-layer encryption
    - Sequential decryption stages
    - Dynamic code loading
    - Self-modifying code patterns
]]

local MultiStage = {}
MultiStage.__index = MultiStage

local Utils = require(script.Parent.utils)

-- Create new multi-stage obfuscation instance
function MultiStage.new(config)
    local self = setmetatable({}, MultiStage)
    
    self.config = config or {}
    self.stats = {
        stagesCreated = 0,
        layersGenerated = 0,
        dynamicLoaders = 0
    }
    
    -- Configuration for staging
    self.maxStages = self.config.maxStages or 5
    self.encryptionStrength = self.config.encryptionStrength or "high"
    
    return self
end

-- Create multi-stage obfuscated code
function MultiStage:createMultiStageObfuscation(originalCode)
    local stages = {}
    local currentCode = originalCode
    
    -- Create multiple encryption stages
    for stage = 1, self.maxStages do
        local stageData = self:createStage(currentCode, stage)
        table.insert(stages, 1, stageData) -- Insert at beginning for reverse order
        currentCode = stageData.loaderCode
        self.stats.stagesCreated = self.stats.stagesCreated + 1
    end
    
    -- Return the outermost stage (first to execute)
    return stages[1].loaderCode
end

-- Create individual obfuscation stage
function MultiStage:createStage(code, stageNumber)
    local encryptedCode = self:encryptStage(code, stageNumber)
    local loaderCode = self:generateStageLoader(encryptedCode, stageNumber)
    
    self.stats.layersGenerated = self.stats.layersGenerated + 1
    
    return {
        encryptedCode = encryptedCode,
        loaderCode = loaderCode,
        stageNumber = stageNumber
    }
end

-- Encrypt code for specific stage
function MultiStage:encryptStage(code, stageNumber)
    local key = self:generateStageKey(stageNumber)
    local encrypted = ""
    
    -- Multi-layer encryption based on stage number
    for layer = 1, stageNumber do
        code = self:applyEncryptionLayer(code, key, layer)
    end
    
    return code
end

-- Generate stage-specific encryption key
function MultiStage:generateStageKey(stageNumber)
    local key = {}
    local baseSeed = stageNumber * 1337 + 42
    
    for i = 1, 64 do -- 64-byte key
        local keyByte = ((baseSeed * i * 31) + (stageNumber * i * 17)) % 256
        table.insert(key, keyByte)
    end
    
    return key
end

-- Apply encryption layer
function MultiStage:applyEncryptionLayer(data, key, layer)
    local result = ""
    
    for i = 1, #data do
        local byte = data:byte(i)
        local keyIndex = ((i - 1) % #key) + 1
        local keyByte = key[keyIndex]
        
        -- Apply layer-specific transformation
        if layer == 1 then
            -- XOR encryption
            byte = bit32.bxor(byte, keyByte)
        elseif layer == 2 then
            -- Addition with overflow
            byte = (byte + keyByte) % 256
        elseif layer == 3 then
            -- Bit rotation
            byte = bit32.lrotate(byte, keyByte % 8)
        elseif layer == 4 then
            -- Substitution cipher
            byte = ((byte * 7) + keyByte) % 256
        else
            -- Complex transformation
            byte = bit32.bxor((byte + keyByte) % 256, bit32.lrotate(keyByte, 3))
        end
        
        result = result .. string.char(byte)
    end
    
    return result
end

-- Generate stage loader code
function MultiStage:generateStageLoader(encryptedCode, stageNumber)
    local loaderVar = Utils.generateRandomVariableName()
    local dataVar = Utils.generateRandomVariableName()
    local keyVar = Utils.generateRandomVariableName()
    local decryptorVar = Utils.generateRandomVariableName()
    local executorVar = Utils.generateRandomVariableName()
    
    -- Generate stage key
    local key = self:generateStageKey(stageNumber)
    local keyString = table.concat(key, ",")
    
    local loaderCode = string.format([[
-- Multi-stage obfuscation loader (Stage %d)
local %s = "%s"
local %s = {%s}
local %s = function(data, key, stages)
    local result = data
    
    -- Apply reverse decryption for each stage
    for stage = stages, 1, -1 do
        local temp = ""
        for i = 1, #result do
            local byte = result:byte(i)
            local keyIndex = ((i - 1) %% #key) + 1
            local keyByte = key[keyIndex]
            
            -- Reverse layer-specific transformation
            if stage == 1 then
                byte = bit32.bxor(byte, keyByte)
            elseif stage == 2 then
                byte = (byte - keyByte + 256) %% 256
            elseif stage == 3 then
                byte = bit32.rrotate(byte, keyByte %% 8)
            elseif stage == 4 then
                -- Reverse substitution (modular inverse)
                byte = ((byte - keyByte + 256) * 183) %% 256
            else
                byte = (bit32.bxor(byte, bit32.lrotate(keyByte, 3)) - keyByte + 256) %% 256
            end
            
            temp = temp .. string.char(byte)
        end
        result = temp
    end
    
    return result
end

local %s = function(code)
    -- Anti-tampering check
    if type(code) ~= "string" or #code == 0 then
        error("Invalid code payload")
    end
    
    -- Dynamic execution with environment protection
    local chunk, err = load(code, "stage_%d", "t", _ENV)
    if not chunk then
        error("Failed to load stage: " .. tostring(err))
    end
    
    return chunk()
end

-- Execute decrypted stage
%s(%s(%s, %s, %d))
]], 
        stageNumber,
        dataVar, Utils.escapeString(encryptedCode),
        keyVar, keyString,
        decryptorVar,
        executorVar, stageNumber,
        executorVar, decryptorVar, dataVar, keyVar, stageNumber
    )
    
    self.stats.dynamicLoaders = self.stats.dynamicLoaders + 1
    return loaderCode
end

-- Create self-modifying code pattern
function MultiStage:createSelfModifyingPattern(code)
    local modifierVar = Utils.generateRandomVariableName()
    local codeVar = Utils.generateRandomVariableName()
    local executorVar = Utils.generateRandomVariableName()
    
    -- Split code into chunks
    local chunks = self:splitCodeIntoChunks(code, 3)
    local chunkVars = {}
    
    local pattern = "-- Self-modifying code pattern\n"
    
    -- Generate variables for each chunk
    for i, chunk in ipairs(chunks) do
        local chunkVar = Utils.generateRandomVariableName()
        table.insert(chunkVars, chunkVar)
        pattern = pattern .. string.format('local %s = "%s"\n', 
            chunkVar, Utils.escapeString(chunk))
    end
    
    -- Generate modifier function
    pattern = pattern .. string.format([[
local %s = function()
    local %s = %s
    
    -- Runtime code modification
    for i = 1, #%s do
        local char = %s:sub(i, i)
        if char:match("[a-zA-Z]") then
            -- Modify alphabetic characters
            local newChar = string.char(((char:byte() - 65) %% 26) + 65)
            %s = %s:gsub(char, newChar, 1)
        end
    end
    
    return %s
end

local %s = function(modifiedCode)
    return load(modifiedCode)()
end

-- Execute self-modified code
%s(%s())
]], 
        modifierVar,
        codeVar, table.concat(chunkVars, " .. "),
        codeVar, codeVar,
        codeVar, codeVar,
        codeVar,
        executorVar,
        executorVar, modifierVar
    )
    
    return pattern
end

-- Split code into chunks for processing
function MultiStage:splitCodeIntoChunks(code, numChunks)
    local chunks = {}
    local chunkSize = math.ceil(#code / numChunks)
    
    for i = 1, numChunks do
        local startPos = (i - 1) * chunkSize + 1
        local endPos = math.min(i * chunkSize, #code)
        local chunk = code:sub(startPos, endPos)
        table.insert(chunks, chunk)
    end
    
    return chunks
end

-- Create dynamic code fetcher (simulates remote code loading)
function MultiStage:createDynamicFetcher(code)
    local fetcherVar = Utils.generateRandomVariableName()
    local storageVar = Utils.generateRandomVariableName()
    local loaderVar = Utils.generateRandomVariableName()
    
    -- Simulate code storage
    local encodedCode = self:encodeForStorage(code)
    
    local fetcher = string.format([[
-- Dynamic code fetcher
local %s = {
    data = "%s",
    checksum = %d
}

local %s = function(storage)
    -- Verify integrity
    local calculatedChecksum = 0
    for i = 1, #storage.data do
        calculatedChecksum = calculatedChecksum + storage.data:byte(i)
    end
    
    if calculatedChecksum ~% 1000 ~= storage.checksum then
        error("Code integrity check failed")
    end
    
    -- Decode and return
    return %s(storage.data)
end

local %s = function(data)
    -- Simple decoding (reverse of encoding)
    local result = ""
    for i = 1, #data do
        local byte = data:byte(i)
        result = result .. string.char((byte - 42) %% 256)
    end
    return result
end

-- Fetch and execute
load(%s(%s))()
]], 
        storageVar, encodedCode, self:calculateChecksum(encodedCode),
        fetcherVar,
        "decode",
        loaderVar,
        fetcherVar, storageVar
    )
    
    return fetcher
end

-- Encode code for storage
function MultiStage:encodeForStorage(code)
    local encoded = ""
    for i = 1, #code do
        local byte = code:byte(i)
        encoded = encoded .. string.char((byte + 42) % 256)
    end
    return encoded
end

-- Calculate simple checksum
function MultiStage:calculateChecksum(data)
    local checksum = 0
    for i = 1, #data do
        checksum = checksum + data:byte(i)
    end
    return checksum % 1000
end

-- Get statistics
function MultiStage:getStats()
    return {
        stagesCreated = self.stats.stagesCreated,
        layersGenerated = self.stats.layersGenerated,
        dynamicLoaders = self.stats.dynamicLoaders
    }
end

return MultiStage
