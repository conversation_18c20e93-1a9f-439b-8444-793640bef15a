/**
 * Luau Obfuscator Discord Bot
 * 
 * A Discord bot that provides a simple interface for obfuscating Luau/Lua code.
 * Users can upload files and receive obfuscated versions back.
 */

require('dotenv').config();
const { Client, GatewayIntentBits, SlashCommandBuilder, REST, Routes, AttachmentBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const LuauObfuscator = require('./obfuscator');

// Bot configuration
const config = {
    token: process.env.DISCORD_TOKEN,
    clientId: process.env.CLIENT_ID,
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 1048576, // 1MB default
    allowedExtensions: (process.env.ALLOWED_EXTENSIONS || '.lua,.luau,.txt').split(','),
    rateLimitRequests: parseInt(process.env.RATE_LIMIT_REQUESTS) || 5,
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000 // 1 minute
};

// Rate limiting
const rateLimits = new Map();

// Create Discord client
const client = new Client({
    intents: [GatewayIntentBits.Guilds]
});

// Slash command definition
const commands = [
    new SlashCommandBuilder()
        .setName('obf')
        .setDescription('Obfuscate a Lua/Luau file')
        .addAttachmentOption(option =>
            option.setName('file')
                .setDescription('The Lua/Luau file to obfuscate')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('preset')
                .setDescription('Obfuscation preset to use')
                .setRequired(false)
                .addChoices(
                    { name: 'Basic (Default)', value: 'basic' },
                    { name: 'Secure', value: 'secure' },
                    { name: 'Minimal', value: 'minimal' },
                    { name: 'Extreme (Maximum Protection)', value: 'extreme' }
                ))
        .addBooleanOption(option =>
            option.setName('stats')
                .setDescription('Show obfuscation statistics')
                .setRequired(false)),
    
    new SlashCommandBuilder()
        .setName('help')
        .setDescription('Show help information about the obfuscator bot'),
    
    new SlashCommandBuilder()
        .setName('presets')
        .setDescription('Show available obfuscation presets')
];

// Obfuscation presets
const presets = {
    basic: {
        stringObfuscation: true,
        structureObfuscation: true,
        antiAnalysis: false,
        environmentManipulation: false,
        compressionLevel: 'medium',
        advancedStringObfuscation: false,
        controlFlowObfuscation: false,
        antiTampering: false,
        deadCodeInjection: false,
        polymorphicObfuscation: false,
        unicodeObfuscation: false,
        opaquePredicates: false
    },
    secure: {
        stringObfuscation: true,
        structureObfuscation: true,
        antiAnalysis: true,
        environmentManipulation: true,
        enableVMDetection: true,
        enableDynamicExecution: true,
        compressionLevel: 'high',
        advancedStringObfuscation: true,
        controlFlowObfuscation: true,
        antiTampering: true,
        deadCodeInjection: true,
        polymorphicObfuscation: true,
        unicodeObfuscation: false, // Disabled for compatibility
        opaquePredicates: true,
        // New advanced features
        encryptedFunctionNames: true,
        dynamicStringGeneration: true,
        multiStageObfuscation: false, // Disabled for compatibility
        antiSandboxDetection: true,
        maxObfuscationStages: 2
    },
    minimal: {
        stringObfuscation: true,
        structureObfuscation: false,
        antiAnalysis: false,
        environmentManipulation: false,
        compressionLevel: 'low',
        advancedStringObfuscation: false,
        controlFlowObfuscation: false,
        antiTampering: false,
        deadCodeInjection: false,
        polymorphicObfuscation: false,
        unicodeObfuscation: false,
        opaquePredicates: false
    },
    extreme: {
        stringObfuscation: true,
        structureObfuscation: true,
        antiAnalysis: true,
        environmentManipulation: true,
        enableVMDetection: true,
        enableDynamicExecution: true,
        compressionLevel: 'high',
        advancedStringObfuscation: true,
        controlFlowObfuscation: true,
        antiTampering: true,
        deadCodeInjection: true,
        polymorphicObfuscation: true,
        unicodeObfuscation: true,
        opaquePredicates: true,
        // New advanced features - all enabled for maximum protection
        encryptedFunctionNames: true,
        dynamicStringGeneration: true,
        multiStageObfuscation: true,
        antiSandboxDetection: true,
        maxObfuscationStages: 5,
        bytecodeEmbedding: false // Still disabled due to complexity
    }
};

/**
 * Check rate limit for user
 */
function checkRateLimit(userId) {
    const now = Date.now();
    const userLimits = rateLimits.get(userId) || { requests: 0, resetTime: now + config.rateLimitWindow };
    
    if (now > userLimits.resetTime) {
        userLimits.requests = 0;
        userLimits.resetTime = now + config.rateLimitWindow;
    }
    
    if (userLimits.requests >= config.rateLimitRequests) {
        return false;
    }
    
    userLimits.requests++;
    rateLimits.set(userId, userLimits);
    return true;
}

/**
 * Validate file attachment
 */
function validateFile(attachment) {
    // Check file size
    if (attachment.size > config.maxFileSize) {
        return { valid: false, error: `File too large. Maximum size is ${Math.round(config.maxFileSize / 1024)}KB.` };
    }
    
    // Check file extension
    const extension = path.extname(attachment.name).toLowerCase();
    if (!config.allowedExtensions.includes(extension)) {
        return { valid: false, error: `Invalid file type. Allowed extensions: ${config.allowedExtensions.join(', ')}` };
    }
    
    return { valid: true };
}

/**
 * Download file content
 */
async function downloadFile(attachment) {
    try {
        const response = await fetch(attachment.url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.text();
    } catch (error) {
        throw new Error(`Failed to download file: ${error.message}`);
    }
}

/**
 * Format file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format obfuscation statistics
 */
function formatStats(stats, originalSize, obfuscatedSize, processingTime) {
    let statsText = `**📊 Obfuscation Statistics:**
• **Strings obfuscated:** ${stats.stringObfuscations.stringsObfuscated}
• **Variables renamed:** ${stats.structureObfuscations.variablesRenamed}
• **Encoding layers:** ${stats.stringObfuscations.encodingLayers}
• **Dynamic constructions:** ${stats.stringObfuscations.dynamicConstructions}`;

    // Add advanced features stats if available
    if (stats.dynamicStrings) {
        statsText += `\n• **Dynamic strings generated:** ${stats.dynamicStrings.dynamicStringsGenerated}`;
    }
    if (stats.multiStageObfuscation) {
        statsText += `\n• **Obfuscation stages:** ${stats.multiStageObfuscation.stagesCreated}`;
    }
    if (stats.antiSandboxDetection) {
        statsText += `\n• **Sandbox checks:** ${stats.antiSandboxDetection.sandboxChecks}`;
    }
    if (stats.bytecodeEmbedding) {
        statsText += `\n• **Bytecodes embedded:** ${stats.bytecodeEmbedding.bytecodesEmbedded}`;
    }

    statsText += `
• **Original size:** ${formatFileSize(originalSize)}
• **Obfuscated size:** ${formatFileSize(obfuscatedSize)}
• **Size increase:** ${((obfuscatedSize / originalSize - 1) * 100).toFixed(1)}%
• **Processing time:** ${processingTime.toFixed(2)}ms`;

    return statsText;
}

// Bot event handlers
client.once('ready', () => {
    console.log(`🤖 Bot logged in as ${client.user.tag}`);
    console.log(`🔧 Serving ${client.guilds.cache.size} guilds`);
});

client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    const { commandName, user } = interaction;

    try {
        // Check rate limit
        if (!checkRateLimit(user.id)) {
            await interaction.reply({
                content: '⏰ **Rate limit exceeded!** Please wait before making another request.',
                ephemeral: true
            });
            return;
        }

        if (commandName === 'obf') {
            await handleObfuscateCommand(interaction);
        } else if (commandName === 'help') {
            await handleHelpCommand(interaction);
        } else if (commandName === 'presets') {
            await handlePresetsCommand(interaction);
        }
    } catch (error) {
        console.error('Command error:', error);
        
        const errorMessage = '❌ **An error occurred while processing your request.** Please try again later.';
        
        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ content: errorMessage, ephemeral: true });
        } else {
            await interaction.reply({ content: errorMessage, ephemeral: true });
        }
    }
});

/**
 * Handle obfuscate command
 */
async function handleObfuscateCommand(interaction) {
    await interaction.deferReply();

    const attachment = interaction.options.getAttachment('file');
    const preset = interaction.options.getString('preset') || 'basic';
    const showStats = interaction.options.getBoolean('stats') || false;

    // Validate file
    const validation = validateFile(attachment);
    if (!validation.valid) {
        await interaction.editReply(`❌ **Error:** ${validation.error}`);
        return;
    }

    try {
        // Download file content
        const sourceCode = await downloadFile(attachment);
        
        if (sourceCode.trim().length === 0) {
            await interaction.editReply('❌ **Error:** File is empty or contains no readable content.');
            return;
        }

        // Create obfuscator with selected preset
        const obfuscatorConfig = presets[preset] || presets.basic;
        const obfuscator = new LuauObfuscator(obfuscatorConfig);

        // Obfuscate code
        const startTime = Date.now();
        const obfuscatedCode = obfuscator.obfuscate(sourceCode);
        const endTime = Date.now();
        const processingTime = endTime - startTime;

        // Get statistics
        const stats = obfuscator.getStats();

        // Create output filename
        const originalName = path.parse(attachment.name).name;
        const extension = path.extname(attachment.name);
        const outputFilename = `${originalName}_obfuscated${extension}`;

        // Create attachment
        const buffer = Buffer.from(obfuscatedCode, 'utf8');
        const fileAttachment = new AttachmentBuilder(buffer, { name: outputFilename });

        // Prepare response
        let responseContent = `✅ **Obfuscation complete!**
• **Preset:** ${preset}
• **Processing time:** ${processingTime}ms
• **Original file:** ${attachment.name}
• **Output file:** ${outputFilename}`;

        if (showStats) {
            responseContent += '\n\n' + formatStats(stats, sourceCode.length, obfuscatedCode.length, processingTime);
        }

        await interaction.editReply({
            content: responseContent,
            files: [fileAttachment]
        });

    } catch (error) {
        console.error('Obfuscation error:', error);
        await interaction.editReply(`❌ **Obfuscation failed:** ${error.message}`);
    }
}

/**
 * Handle help command
 */
async function handleHelpCommand(interaction) {
    const helpEmbed = {
        color: 0x0099ff,
        title: '🔐 Luau Obfuscator Bot',
        description: 'A powerful Discord bot for obfuscating Lua/Luau code files.',
        fields: [
            {
                name: '📝 Commands',
                value: '• `/obf` - Obfuscate a Lua/Luau file\n• `/help` - Show this help message\n• `/presets` - Show available presets',
                inline: false
            },
            {
                name: '📁 Supported Files',
                value: `• **Extensions:** ${config.allowedExtensions.join(', ')}\n• **Max size:** ${formatFileSize(config.maxFileSize)}`,
                inline: true
            },
            {
                name: '⚡ Rate Limits',
                value: `• **Requests:** ${config.rateLimitRequests} per minute\n• **Per user:** Individual limits`,
                inline: true
            },
            {
                name: '🚀 Usage Example',
                value: '1. Use `/obf` command\n2. Upload your Lua file\n3. Choose a preset (optional)\n4. Enable stats (optional)\n5. Download obfuscated file',
                inline: false
            }
        ],
        footer: {
            text: 'Luau Advanced Obfuscator • Secure your code'
        }
    };

    await interaction.reply({ embeds: [helpEmbed] });
}

/**
 * Handle presets command
 */
async function handlePresetsCommand(interaction) {
    const presetsEmbed = {
        color: 0x00ff99,
        title: '⚙️ Obfuscation Presets',
        description: 'Choose from different obfuscation levels based on your needs.',
        fields: [
            {
                name: '🔹 Basic (Default)',
                value: '• String obfuscation\n• Variable renaming\n• Medium compression\n• **Best for:** General use',
                inline: true
            },
            {
                name: '🔸 Secure',
                value: '• All basic features\n• Anti-analysis\n• Environment manipulation\n• Control flow obfuscation\n• Encrypted function names\n• Dynamic string generation\n• **Best for:** Sensitive code',
                inline: true
            },
            {
                name: '🔹 Minimal',
                value: '• String obfuscation only\n• Low compression\n• Fast processing\n• **Best for:** Quick obfuscation',
                inline: true
            },
            {
                name: '🔴 Extreme',
                value: '• All security features\n• Advanced string encryption\n• Multi-stage obfuscation\n• Anti-sandbox detection\n• Dead code injection\n• Anti-tampering\n• **Best for:** Maximum protection',
                inline: true
            }
        ],
        footer: {
            text: 'Use the preset option in /obf command to select'
        }
    };

    await interaction.reply({ embeds: [presetsEmbed] });
}

// Register slash commands
async function registerCommands() {
    const rest = new REST({ version: '10' }).setToken(config.token);

    try {
        console.log('🔄 Registering slash commands...');
        
        await rest.put(
            Routes.applicationCommands(config.clientId),
            { body: commands }
        );
        
        console.log('✅ Slash commands registered successfully');
    } catch (error) {
        console.error('❌ Failed to register slash commands:', error);
    }
}

// Start the bot
async function startBot() {
    if (!config.token) {
        console.error('❌ DISCORD_TOKEN is required in .env file');
        process.exit(1);
    }

    if (!config.clientId) {
        console.error('❌ CLIENT_ID is required in .env file');
        process.exit(1);
    }

    await registerCommands();
    await client.login(config.token);
}

// Error handling
process.on('unhandledRejection', error => {
    console.error('Unhandled promise rejection:', error);
});

process.on('uncaughtException', error => {
    console.error('Uncaught exception:', error);
    process.exit(1);
});

// Start the bot
startBot();
